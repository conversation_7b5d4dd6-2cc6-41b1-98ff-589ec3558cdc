import { useState, useCallback, useRef, useEffect } from "react";
import { ConversationState, Message, ApiKeys, CHARACTERS } from "@/types/voice";
import { AIService } from "@/services/aiService";
import {
	SpeechService,
	SpeechRecognitionService,
} from "@/services/speechService";
import { useToast } from "@/hooks/use-toast";

export function useVoiceDiscussion() {
	const [conversation, setConversation] = useState<ConversationState>({
		topic: "",
		messages: [],
		isActive: false,
		currentSpeaker: null,
		userHasRaised: false,
	});

	const [isPaused, setIsPaused] = useState(false);

	const [apiKeys, setApiKeys] = useState<ApiKeys>({
		geminiKey: localStorage.getItem("voiceverse_gemini_key") || "",
		elevenLabsKey: localStorage.getItem("voiceverse_elevenlabs_key") || "",
	});

	const [isListening, setIsListening] = useState(false);
	const [micPermissionGranted, setMicPermissionGranted] = useState(false);
	const [isLoading, setIsLoading] = useState(false);
	const [isRespondingToUser, setIsRespondingToUser] = useState(false);

	const aiServiceRef = useRef<AIService | null>(null);
	const speechServiceRef = useRef<SpeechService | null>(null);
	const speechRecognitionRef = useRef<SpeechRecognitionService | null>(null);
	const conversationIntervalRef = useRef<NodeJS.Timeout | null>(null);
	const isGeneratingRef = useRef<boolean>(false);

	const { toast } = useToast();

	// Initialize services when API keys change
	useEffect(() => {
		if (apiKeys.geminiKey) {
			aiServiceRef.current = new AIService(apiKeys.geminiKey);
		}
		if (apiKeys.elevenLabsKey) {
			speechServiceRef.current = new SpeechService(apiKeys.elevenLabsKey);
		}
		if (!speechRecognitionRef.current) {
			speechRecognitionRef.current = new SpeechRecognitionService();
		}
	}, [apiKeys]);

	// Save API keys to localStorage
	const handleApiKeysChange = useCallback((keys: ApiKeys) => {
		setApiKeys(keys);
		localStorage.setItem("voiceverse_gemini_key", keys.geminiKey);
		localStorage.setItem("voiceverse_elevenlabs_key", keys.elevenLabsKey);
	}, []);

	// Request microphone permission
	const requestMicPermission = useCallback(async (): Promise<boolean> => {
		if (!speechRecognitionRef.current?.isSupported()) {
			toast({
				title: "Speech Recognition Not Supported",
				description: "Your browser doesn't support speech recognition.",
				variant: "destructive",
			});
			return false;
		}

		try {
			const granted = await speechRecognitionRef.current.requestPermission();
			setMicPermissionGranted(granted);
			return granted;
		} catch (error) {
			console.error("Microphone permission error:", error);
			return false;
		}
	}, [toast]);

	// Add message to conversation
	const addMessage = useCallback(
		(characterId: string, content: string, isUser = false) => {
			const message: Message = {
				id: Date.now().toString() + Math.random(),
				characterId,
				content,
				timestamp: new Date(),
				isUser,
			};

			setConversation((prev) => ({
				...prev,
				messages: [...prev.messages, message],
			}));

			return message;
		},
		[]
	);

	// Play character voice
	const playCharacterVoice = useCallback(
		async (characterId: string, text: string) => {
			if (!speechServiceRef.current) return;

			const character = CHARACTERS.find((c) => c.id === characterId);
			if (!character) return;

			try {
				setConversation((prev) => ({ ...prev, currentSpeaker: characterId }));
				await speechServiceRef.current.speak(text, character.voiceId, {
					playbackRate: 0.9,
				});
			} catch (error) {
				console.error("Voice playback error:", error);
				toast({
					title: "Voice Playback Error",
					description:
						"Failed to play character voice. Check your ElevenLabs API key.",
					variant: "destructive",
				});
			} finally {
				setConversation((prev) => ({ ...prev, currentSpeaker: null }));
			}
		},
		[toast]
	);

	// Generate and play AI responses
	const generateAIResponses = useCallback(
		async (userMessage?: string) => {
			console.log(
				"🔄 generateAIResponses called with userMessage:",
				userMessage
			);

			if (!aiServiceRef.current) {
				console.log("❌ No AI service available");
				return;
			}
			// Do not generate while user has the hand raised (user intends to speak)
			if (conversation.userHasRaised) {
				console.log("❌ User has hand raised, skipping AI generation");
				return;
			}
			// Skip auto-generation if we're responding to user (unless this IS the user response)
			if (isRespondingToUser && !userMessage) {
				console.log(
					"❌ Currently responding to user, skipping auto-generation"
				);
				return;
			}
			// Avoid speaking over current audio or concurrent generations
			if (isGeneratingRef.current) {
				console.log("❌ Already generating, skipping");
				return;
			}
			if (speechServiceRef.current?.isPlaying()) {
				console.log("❌ Audio currently playing, skipping");
				return;
			}

			try {
				isGeneratingRef.current = true;
				setIsLoading(true);

				console.log("🤖 Calling AI service with:", {
					topic: conversation.topic,
					messageCount: conversation.messages.length,
					userMessage: userMessage,
				});

				const responses = await aiServiceRef.current.generateConversation(
					conversation.topic,
					conversation.messages,
					userMessage
				);

				console.log("✅ AI service returned responses:", responses);

				for (let i = 0; i < responses.length; i++) {
					const response = responses[i];

					// Check if user raised hand during generation
					if (conversation.userHasRaised) {
						break;
					}

					console.log(
						`🎭 Character ${response.characterId} responding: "${response.content}"`
					);

					addMessage(response.characterId, response.content);

					// Longer pre-speech pause for more natural timing
					const preDelay = userMessage ? 1500 : 800; // Longer delay for user responses
					await new Promise((resolve) => setTimeout(resolve, preDelay));

					// Check again before playing voice
					if (conversation.userHasRaised) {
						break;
					}

					await playCharacterVoice(response.characterId, response.content);

					// Much longer post-speech pause, especially between multiple characters
					const postDelay = i < responses.length - 1 ? 4000 : 3000; // 4s between characters, 3s after last
					const userResponseDelay = userMessage ? 2000 : 0; // Extra delay for user responses

					console.log(
						`⏱️ Waiting ${
							postDelay + userResponseDelay
						}ms before next response...`
					);
					await new Promise((resolve) =>
						setTimeout(resolve, postDelay + userResponseDelay)
					);
				}
			} catch (error) {
				console.error("AI response error:", error);
				toast({
					title: "AI Response Error",
					description:
						"Failed to generate AI responses. Check your Gemini API key.",
					variant: "destructive",
				});
			} finally {
				isGeneratingRef.current = false;
				setIsLoading(false);
			}
		},
		[
			conversation.topic,
			conversation.messages,
			conversation.userHasRaised,
			isRespondingToUser,
			addMessage,
			playCharacterVoice,
			toast,
		]
	);

	// Start conversation with topic
	const startConversation = useCallback(
		async (topic: string) => {
			if (!aiServiceRef.current || !speechServiceRef.current) {
				toast({
					title: "Configuration Required",
					description: "Please configure your API keys in settings.",
					variant: "destructive",
				});
				return;
			}

			setConversation((prev) => ({
				...prev,
				topic,
				messages: [],
				isActive: true,
				currentSpeaker: null,
				userHasRaised: false,
			}));

			// Start with initial AI discussion
			setTimeout(() => generateAIResponses(), 1000);

			// Set up periodic conversation continuation
			conversationIntervalRef.current = setInterval(() => {
				if (!conversation.userHasRaised && !isPaused) {
					generateAIResponses();
				}
			}, 15000); // Continue conversation every 15 seconds if user hasn't raised hand
		},
		[generateAIResponses, toast]
	);

	// Stop conversation
	const stopConversation = useCallback(() => {
		if (conversationIntervalRef.current) {
			clearInterval(conversationIntervalRef.current);
			conversationIntervalRef.current = null;
		}

		speechServiceRef.current?.stopCurrentAudio();
		speechRecognitionRef.current?.stopListening();

		setConversation((prev) => ({
			...prev,
			isActive: false,
			currentSpeaker: null,
			userHasRaised: false,
		}));
		setIsListening(false);
	}, []);

	// Toggle conversation
	const toggleConversation = useCallback(() => {
		if (conversation.isActive) {
			// Pause the conversation
			setIsPaused(true);
			if (conversationIntervalRef.current) {
				clearInterval(conversationIntervalRef.current);
				conversationIntervalRef.current = null;
			}
			speechServiceRef.current?.stopCurrentAudio();
			setConversation((prev) => ({
				...prev,
				isActive: false,
				currentSpeaker: null,
			}));
		} else if (conversation.topic) {
			// Resume or start the conversation
			setIsPaused(false);
			setConversation((prev) => ({ ...prev, isActive: true }));

			// Resume auto-conversation if user hasn't raised hand
			if (!conversation.userHasRaised) {
				setTimeout(() => generateAIResponses(), 1000);
				conversationIntervalRef.current = setInterval(() => {
					if (!conversation.userHasRaised) {
						generateAIResponses();
					}
				}, 15000);
			}
		}
	}, [
		conversation.isActive,
		conversation.topic,
		conversation.userHasRaised,
		generateAIResponses,
	]);

	// Clear all messages but keep topic and state
	const clearMessages = useCallback(() => {
		setConversation((prev) => ({
			...prev,
			messages: [],
		}));
	}, []);

	// Restart the conversation for the same topic
	const restartConversation = useCallback(() => {
		if (
			!conversation.topic ||
			!aiServiceRef.current ||
			!speechServiceRef.current
		) {
			toast({
				title: "Cannot Restart",
				description: "Missing topic or API keys.",
				variant: "destructive",
			});
			return;
		}

		// Clear any existing timers and audio
		if (conversationIntervalRef.current) {
			clearInterval(conversationIntervalRef.current);
			conversationIntervalRef.current = null;
		}
		speechServiceRef.current?.stopCurrentAudio();

		// Reset state and start again
		setIsPaused(false);
		setConversation((prev) => ({
			...prev,
			messages: [],
			isActive: true,
			currentSpeaker: null,
			userHasRaised: false,
		}));

		setTimeout(() => generateAIResponses(), 800);
		conversationIntervalRef.current = setInterval(() => {
			if (!conversation.userHasRaised) {
				generateAIResponses();
			}
		}, 15000);
	}, [
		conversation.topic,
		conversation.userHasRaised,
		generateAIResponses,
		toast,
	]);

	// Raise hand to join conversation
	const raiseHand = useCallback(() => {
		// Prevent action if currently listening
		if (isListening) {
			toast({
				title: "Cannot Change Hand State",
				description: "Please stop speaking first.",
				variant: "destructive",
				duration: 2000,
			});
			return;
		}

		const newRaisedState = !conversation.userHasRaised;

		setConversation((prev) => ({
			...prev,
			userHasRaised: newRaisedState,
		}));

		if (newRaisedState) {
			// User is raising hand - pause auto-conversation
			if (conversationIntervalRef.current) {
				clearInterval(conversationIntervalRef.current);
				conversationIntervalRef.current = null;
			}
			// Stop any speaking so user can interrupt immediately
			speechServiceRef.current?.stopCurrentAudio();
			toast({
				title: "Hand Raised ✋",
				description: "Click the microphone to speak when ready.",
				duration: 3000,
			});
		} else {
			// User is lowering hand - resume auto-conversation
			if (conversation.isActive && !isPaused) {
				conversationIntervalRef.current = setInterval(() => {
					generateAIResponses();
				}, 15000);
			}
			toast({
				title: "Hand Lowered 👋",
				description: "Auto-conversation resumed.",
				duration: 3000,
			});
		}
	}, [
		conversation.userHasRaised,
		conversation.isActive,
		isPaused,
		isListening,
		generateAIResponses,
		toast,
	]);

	// Start listening for user voice
	const startListening = useCallback(async () => {
		if (!speechRecognitionRef.current || !micPermissionGranted) {
			await requestMicPermission();
			return;
		}

		try {
			setIsListening(true);
			// Ensure any ongoing TTS is stopped before user speaks
			speechServiceRef.current?.stopCurrentAudio();
			const transcript = await speechRecognitionRef.current.listen();

			if (transcript.trim()) {
				console.log("🎤 User spoke:", transcript);

				// IMMEDIATELY stop any auto-conversation
				if (conversationIntervalRef.current) {
					clearInterval(conversationIntervalRef.current);
					conversationIntervalRef.current = null;
					console.log("🛑 Auto-conversation STOPPED for user input");
				}

				// Stop any current AI generation
				isGeneratingRef.current = false;

				// Stop any current TTS
				speechServiceRef.current?.stopCurrentAudio();

				// Add user message
				addMessage("user", transcript, true);

				// Lower hand before generating so characters can respond immediately
				setConversation((prev) => ({ ...prev, userHasRaised: false }));

				// Wait longer for the message to be properly added to state
				await new Promise((resolve) => setTimeout(resolve, 500));

				console.log("🤖 Generating AI responses to user input...");
				console.log("📝 User transcript being sent to AI:", transcript);

				// Set flag to prevent auto-conversation interference
				setIsRespondingToUser(true);

				// Generate AI responses to user input with explicit user message
				await generateAIResponses(transcript);

				console.log("✅ AI response generation completed");

				// Clear the flag after a delay
				setTimeout(() => {
					setIsRespondingToUser(false);
					console.log("🔓 User response mode cleared");
				}, 10000); // Keep user response mode for 10 seconds

				// Resume auto-conversation after a much longer delay (30 seconds)
				setTimeout(() => {
					console.log("🔄 Resuming auto-conversation after user interaction");
					if (conversation.isActive && !isPaused) {
						conversationIntervalRef.current = setInterval(() => {
							generateAIResponses();
						}, 15000);
					}
				}, 30000); // 30 second delay before resuming auto-conversation
			}
		} catch (error) {
			console.error("Speech recognition error:", error);
			toast({
				title: "Speech Recognition Error",
				description: "Failed to transcribe speech. Please try again.",
				variant: "destructive",
			});
		} finally {
			setIsListening(false);
		}
	}, [
		micPermissionGranted,
		requestMicPermission,
		addMessage,
		generateAIResponses,
		conversation.isActive,
		isPaused,
		toast,
	]);

	// Stop listening
	const stopListening = useCallback(() => {
		speechRecognitionRef.current?.stopListening();
		setIsListening(false);
	}, []);

	// Cleanup on unmount
	useEffect(() => {
		return () => {
			if (conversationIntervalRef.current) {
				clearInterval(conversationIntervalRef.current);
			}
			speechServiceRef.current?.stopCurrentAudio();
			speechRecognitionRef.current?.stopListening();
		};
	}, []);

	return {
		conversation,
		apiKeys,
		isListening,
		micPermissionGranted,
		isLoading,
		isPaused,
		startConversation,
		stopConversation,
		toggleConversation,
		clearMessages,
		restartConversation,
		raiseHand,
		startListening,
		stopListening,
		handleApiKeysChange,
		requestMicPermission,
	};
}
