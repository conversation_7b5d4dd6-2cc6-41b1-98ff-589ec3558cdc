export class SpeechService {
	private elevenLabsKey: string;
	private currentAudio: HTMLAudioElement | null = null;

	constructor(elevenLabsKey: string) {
		this.elevenLabsKey = elevenLabsKey;
	}

	async speak(
		text: string,
		voiceId: string,
		options?: { playbackRate?: number }
	): Promise<void> {
		try {
			// Stop any currently playing audio
			this.stopCurrentAudio();

			const response = await fetch(
				`https://api.elevenlabs.io/v1/text-to-speech/${voiceId}`,
				{
					method: "POST",
					headers: {
						Accept: "audio/mpeg",
						"Content-Type": "application/json",
						"xi-api-key": this.elevenLabsKey,
					},
					body: JSON.stringify({
						text,
						model_id: "eleven_turbo_v2_5",
						voice_settings: {
							stability: 0.5,
							similarity_boost: 0.8,
							style: 0.0,
							use_speaker_boost: true,
						},
					}),
				}
			);

			if (!response.ok) {
				throw new Error(`ElevenLabs API error: ${response.status}`);
			}

			const audioBlob = await response.blob();
			const audioUrl = URL.createObjectURL(audioBlob);

			this.currentAudio = new Audio(audioUrl);
			if (
				options?.playbackRate &&
				this.currentAudio?.playbackRate !== undefined
			) {
				// Clamp playbackRate to a reasonable range
				const rate = Math.max(0.5, Math.min(1.25, options.playbackRate));
				this.currentAudio.playbackRate = rate;
			}

			return new Promise((resolve, reject) => {
				if (!this.currentAudio) {
					reject(new Error("Audio not initialized"));
					return;
				}

				this.currentAudio.onended = () => {
					URL.revokeObjectURL(audioUrl);
					resolve();
				};

				this.currentAudio.onerror = () => {
					URL.revokeObjectURL(audioUrl);
					reject(new Error("Audio playback failed"));
				};

				this.currentAudio.play().catch(reject);
			});
		} catch (error) {
			console.error("Speech Service Error:", error);
			throw error;
		}
	}

	stopCurrentAudio(): void {
		if (this.currentAudio) {
			this.currentAudio.pause();
			this.currentAudio.currentTime = 0;
			this.currentAudio = null;
		}
	}

	isPlaying(): boolean {
		return this.currentAudio !== null && !this.currentAudio.paused;
	}
}

// Speech Recognition Service
export class SpeechRecognitionService {
	private recognition: any;
	private isListening = false;

	constructor() {
		if ("webkitSpeechRecognition" in window || "SpeechRecognition" in window) {
			const SpeechRecognition =
				(window as any).webkitSpeechRecognition ||
				(window as any).SpeechRecognition;
			this.recognition = new SpeechRecognition();

			this.recognition.continuous = false;
			this.recognition.interimResults = false;
			this.recognition.lang = "en-US";
		}
	}

	async requestPermission(): Promise<boolean> {
		try {
			await navigator.mediaDevices.getUserMedia({ audio: true });
			return true;
		} catch (error) {
			console.error("Microphone permission denied:", error);
			return false;
		}
	}

	isSupported(): boolean {
		return !!this.recognition;
	}

	async listen(): Promise<string> {
		if (!this.recognition) {
			throw new Error("Speech recognition not supported");
		}

		if (this.isListening) {
			throw new Error("Already listening");
		}

		return new Promise((resolve, reject) => {
			this.isListening = true;

			this.recognition.onresult = (event: any) => {
				const transcript = event.results[0][0].transcript;
				this.isListening = false;
				resolve(transcript);
			};

			this.recognition.onerror = (event: any) => {
				this.isListening = false;
				reject(new Error(`Speech recognition error: ${event.error}`));
			};

			this.recognition.onend = () => {
				this.isListening = false;
			};

			this.recognition.start();
		});
	}

	stopListening(): void {
		if (this.recognition && this.isListening) {
			this.recognition.stop();
			this.isListening = false;
		}
	}

	getIsListening(): boolean {
		return this.isListening;
	}
}
