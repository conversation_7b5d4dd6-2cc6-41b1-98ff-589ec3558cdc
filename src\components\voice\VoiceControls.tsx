import { useState } from "react";
import { motion } from "framer-motion";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Pause, Settings } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface VoiceControlsProps {
	isListening: boolean;
	isConversationActive: boolean;
	userHasRaised: boolean;
	onStartListening: () => void;
	onStopListening: () => void;
	onRaiseHand: () => void;
	onToggleConversation: () => void;
	onOpenSettings: () => void;
	disabled?: boolean;
}

export function VoiceControls({
	isListening,
	isConversationActive,
	userHasRaised,
	onStartListening,
	onStopListening,
	onRaiseHand,
	onToggleConversation,
	onOpenSettings,
	disabled = false,
}: VoiceControlsProps) {
	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			className="neu-card p-6"
		>
			<div className="flex items-center justify-between gap-6 mb-4">
				<div className="flex-1" />
				{/* Raise Hand Button */}
				<motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
					<Button
						variant={userHasRaised ? "default" : "outline"}
						size="lg"
						onClick={onRaiseHand}
						disabled={disabled}
						className={cn(
							"neu-card w-20 h-20 rounded-full flex flex-col items-center justify-center gap-1",
							userHasRaised && "bg-primary text-primary-foreground shadow-glow"
						)}
					>
						<Hand
							className={cn(
								"h-6 w-6 transition-transform",
								userHasRaised && "rotate-12"
							)}
						/>
						<span className="text-xs font-medium">
							{userHasRaised ? "Lower" : "Raise"}
						</span>
					</Button>
				</motion.div>

				{/* Microphone Button */}
				<motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
					<Button
						variant={isListening ? "destructive" : "default"}
						size="lg"
						onClick={isListening ? onStopListening : onStartListening}
						disabled={disabled || !userHasRaised}
						className={cn(
							"neu-card w-24 h-24 rounded-full flex flex-col items-center justify-center gap-1",
							isListening && "speaking-pulse shadow-glow",
							!userHasRaised && !isListening && "opacity-50"
						)}
					>
						{isListening ? (
							<MicOff className="h-8 w-8" />
						) : (
							<Mic className="h-8 w-8" />
						)}
						<span className="text-xs font-medium">
							{isListening ? "Stop" : "Speak"}
						</span>
					</Button>
				</motion.div>

				{/* Conversation Toggle */}
				<motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
					<Button
						variant={isConversationActive ? "destructive" : "default"}
						size="lg"
						onClick={onToggleConversation}
						disabled={disabled}
						className="neu-card w-20 h-20 rounded-full flex flex-col items-center justify-center gap-1"
					>
						{isConversationActive ? (
							<Pause className="h-6 w-6" />
						) : (
							<Play className="h-6 w-6" />
						)}
						<span className="text-xs font-medium">
							{isConversationActive ? "Pause" : "Start"}
						</span>
					</Button>
				</motion.div>
				<div className="flex-1" />
			</div>

			<div className="flex items-center justify-center">
				{/* Settings Button */}
				<motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
					<Button
						variant="ghost"
						size="lg"
						onClick={onOpenSettings}
						className="neu-card w-16 h-16 rounded-full"
					>
						<Settings className="h-5 w-5" />
					</Button>
				</motion.div>
			</div>
		</motion.div>
	);
}
