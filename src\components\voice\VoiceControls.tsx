import { useState } from "react";
import { motion } from "framer-motion";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Pause, Settings } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface VoiceControlsProps {
	isListening: boolean;
	isConversationActive: boolean;
	userHasRaised: boolean;
	onStartListening: () => void;
	onStopListening: () => void;
	onRaiseHand: () => void;
	onToggleConversation: () => void;
	onOpenSettings: () => void;
	disabled?: boolean;
}

export function VoiceControls({
	isListening,
	isConversationActive,
	userHasRaised,
	onStartListening,
	onStopListening,
	onRaiseHand,
	onToggleConversation,
	onOpenSettings,
	disabled = false,
}: VoiceControlsProps) {
	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			className="neu-card p-6"
		>
			{/* Title */}
			<div className="text-center mb-6">
				<h3 className="font-semibold text-lg mb-2">Voice Controls</h3>
				<p className="text-xs text-muted-foreground">
					{userHasRaised
						? "Ready to speak - Click microphone to start"
						: "Raise your hand to join the conversation"}
				</p>
			</div>

			{/* Main Controls */}
			<div className="space-y-4">
				{/* Raise Hand Button */}
				<motion.div
					whileHover={{ scale: 1.02 }}
					whileTap={{ scale: 0.98 }}
					className="flex justify-center"
				>
					<Button
						variant={userHasRaised ? "default" : "outline"}
						size="lg"
						onClick={onRaiseHand}
						disabled={disabled}
						className={cn(
							"w-full h-16 rounded-xl flex items-center justify-center gap-3 text-base font-medium transition-all duration-300 border-2",
							userHasRaised
								? "bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white border-blue-400 shadow-lg shadow-blue-500/25 ring-2 ring-blue-400/30"
								: "bg-gradient-to-r from-gray-50 to-gray-100 hover:from-gray-100 hover:to-gray-200 text-gray-700 border-gray-300 hover:border-gray-400 shadow-md hover:shadow-lg dark:from-gray-800 dark:to-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:border-gray-500"
						)}
					>
						<Hand
							className={cn(
								"h-6 w-6 transition-all duration-300",
								userHasRaised && "rotate-12 scale-110 drop-shadow-sm"
							)}
						/>
						<span className="font-semibold">
							{userHasRaised ? "Lower Hand" : "Raise Hand"}
						</span>
					</Button>
				</motion.div>

				{/* Microphone Button */}
				<motion.div
					whileHover={{ scale: 1.02 }}
					whileTap={{ scale: 0.98 }}
					className="flex justify-center"
				>
					<Button
						variant={isListening ? "destructive" : "default"}
						size="lg"
						onClick={isListening ? onStopListening : onStartListening}
						disabled={disabled || !userHasRaised}
						className={cn(
							"w-full h-20 rounded-xl flex items-center justify-center gap-3 text-base font-medium transition-all duration-300 border-2",
							isListening
								? "bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white border-red-400 shadow-lg shadow-red-500/25 ring-2 ring-red-400/30 animate-pulse"
								: userHasRaised
								? "bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white border-green-400 shadow-lg shadow-green-500/25 hover:ring-2 hover:ring-green-400/30"
								: "bg-gradient-to-r from-gray-300 to-gray-400 text-gray-500 border-gray-300 cursor-not-allowed opacity-60 dark:from-gray-700 dark:to-gray-600 dark:text-gray-400 dark:border-gray-600"
						)}
					>
						{isListening ? (
							<>
								<MicOff className="h-8 w-8 animate-pulse drop-shadow-sm" />
								<span className="font-semibold">Stop Speaking</span>
							</>
						) : (
							<>
								<Mic className="h-8 w-8 drop-shadow-sm" />
								<span className="font-semibold">Start Speaking</span>
							</>
						)}
					</Button>
				</motion.div>

				{/* Conversation Toggle */}
				<motion.div
					whileHover={{ scale: 1.02 }}
					whileTap={{ scale: 0.98 }}
					className="flex justify-center"
				>
					<Button
						variant={isConversationActive ? "destructive" : "default"}
						size="lg"
						onClick={onToggleConversation}
						disabled={disabled}
						className={cn(
							"w-full h-16 rounded-xl flex items-center justify-center gap-3 text-base font-medium transition-all duration-300 border-2",
							isConversationActive
								? "bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white border-orange-400 shadow-lg shadow-orange-500/25 ring-2 ring-orange-400/30"
								: "bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 text-white border-emerald-400 shadow-lg shadow-emerald-500/25 hover:ring-2 hover:ring-emerald-400/30"
						)}
					>
						{isConversationActive ? (
							<>
								<Pause className="h-6 w-6 drop-shadow-sm" />
								<span className="font-semibold">Pause Discussion</span>
							</>
						) : (
							<>
								<Play className="h-6 w-6 drop-shadow-sm" />
								<span className="font-semibold">Resume Discussion</span>
							</>
						)}
					</Button>
				</motion.div>

				{/* Settings Button */}
				<motion.div
					whileHover={{ scale: 1.02 }}
					whileTap={{ scale: 0.98 }}
					className="flex justify-center pt-2"
				>
					<Button
						variant="ghost"
						size="lg"
						onClick={onOpenSettings}
						className="w-full h-12 rounded-xl flex items-center justify-center gap-3 text-sm font-medium transition-all duration-300 border-2 border-transparent hover:border-gray-300 bg-gradient-to-r from-gray-50 to-gray-100 hover:from-gray-100 hover:to-gray-200 text-gray-600 hover:text-gray-700 shadow-sm hover:shadow-md dark:from-gray-800 dark:to-gray-700 dark:text-gray-300 dark:hover:text-gray-200 dark:hover:border-gray-600"
					>
						<Settings className="h-5 w-5" />
						<span className="font-medium">Settings</span>
					</Button>
				</motion.div>
			</div>
		</motion.div>
	);
}
