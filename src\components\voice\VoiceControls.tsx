import { useState } from "react";
import { motion } from "framer-motion";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Pause, Settings } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface VoiceControlsProps {
	isListening: boolean;
	isConversationActive: boolean;
	userHasRaised: boolean;
	onStartListening: () => void;
	onStopListening: () => void;
	onRaiseHand: () => void;
	onToggleConversation: () => void;
	onOpenSettings: () => void;
	disabled?: boolean;
}

export function VoiceControls({
	isListening,
	isConversationActive,
	userHasRaised,
	onStartListening,
	onStopListening,
	onRaiseHand,
	onToggleConversation,
	onOpenSettings,
	disabled = false,
}: VoiceControlsProps) {
	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			className="neu-card p-6"
		>
			{/* Title */}
			<div className="text-center mb-6">
				<h3 className="font-semibold text-lg mb-2">Voice Controls</h3>
				<p className="text-xs text-muted-foreground">
					{userHasRaised
						? "Ready to speak - Click microphone to start"
						: "Raise your hand to join the conversation"}
				</p>
			</div>

			{/* Main Controls */}
			<div className="space-y-4">
				{/* Raise Hand Button */}
				<motion.div
					whileHover={{ scale: 1.02 }}
					whileTap={{ scale: 0.98 }}
					className="flex justify-center"
				>
					<Button
						variant={userHasRaised ? "default" : "outline"}
						size="lg"
						onClick={onRaiseHand}
						disabled={disabled}
						className={cn(
							"neu-card w-full h-16 rounded-xl flex items-center justify-center gap-3 text-base font-medium transition-all",
							userHasRaised &&
								"bg-primary text-primary-foreground shadow-glow ring-2 ring-primary/20"
						)}
					>
						<Hand
							className={cn(
								"h-6 w-6 transition-transform duration-300",
								userHasRaised && "rotate-12 scale-110"
							)}
						/>
						<span>{userHasRaised ? "Lower Hand" : "Raise Hand"}</span>
					</Button>
				</motion.div>

				{/* Microphone Button */}
				<motion.div
					whileHover={{ scale: 1.02 }}
					whileTap={{ scale: 0.98 }}
					className="flex justify-center"
				>
					<Button
						variant={isListening ? "destructive" : "default"}
						size="lg"
						onClick={isListening ? onStopListening : onStartListening}
						disabled={disabled || !userHasRaised}
						className={cn(
							"neu-card w-full h-20 rounded-xl flex items-center justify-center gap-3 text-base font-medium transition-all",
							isListening &&
								"speaking-pulse shadow-glow ring-2 ring-destructive/20",
							!userHasRaised && !isListening && "opacity-50 cursor-not-allowed"
						)}
					>
						{isListening ? (
							<>
								<MicOff className="h-8 w-8 animate-pulse" />
								<span>Stop Speaking</span>
							</>
						) : (
							<>
								<Mic className="h-8 w-8" />
								<span>Start Speaking</span>
							</>
						)}
					</Button>
				</motion.div>

				{/* Conversation Toggle */}
				<motion.div
					whileHover={{ scale: 1.02 }}
					whileTap={{ scale: 0.98 }}
					className="flex justify-center"
				>
					<Button
						variant={isConversationActive ? "destructive" : "default"}
						size="lg"
						onClick={onToggleConversation}
						disabled={disabled}
						className={cn(
							"neu-card w-full h-16 rounded-xl flex items-center justify-center gap-3 text-base font-medium transition-all",
							isConversationActive && "ring-2 ring-destructive/20"
						)}
					>
						{isConversationActive ? (
							<>
								<Pause className="h-6 w-6" />
								<span>Pause Discussion</span>
							</>
						) : (
							<>
								<Play className="h-6 w-6" />
								<span>Resume Discussion</span>
							</>
						)}
					</Button>
				</motion.div>

				{/* Settings Button */}
				<motion.div
					whileHover={{ scale: 1.02 }}
					whileTap={{ scale: 0.98 }}
					className="flex justify-center pt-2"
				>
					<Button
						variant="ghost"
						size="lg"
						onClick={onOpenSettings}
						className="neu-card w-full h-12 rounded-xl flex items-center justify-center gap-3 text-sm font-medium"
					>
						<Settings className="h-5 w-5" />
						<span>Settings</span>
					</Button>
				</motion.div>
			</div>
		</motion.div>
	);
}
