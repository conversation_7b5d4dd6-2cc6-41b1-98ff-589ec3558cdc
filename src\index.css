@tailwind base;
@tailwind components;
@tailwind utilities;

/* Voice Verse Simulator - Neumorphic Design System */

@layer base {
  :root {
    /* Neumorphic Light Theme */
    --background: 225 15% 95%;
    --foreground: 225 25% 25%;
    
    --neu-bg: 225 15% 95%;
    --neu-highlight: 225 20% 98%;
    --neu-shadow: 225 10% 88%;
    --neu-border: 225 15% 90%;
    
    --card: 225 15% 95%;
    --card-foreground: 225 25% 25%;
    
    --primary: 260 60% 65%;
    --primary-foreground: 0 0% 100%;
    --primary-glow: 260 80% 75%;
    
    --secondary: 200 50% 70%;
    --secondary-foreground: 225 25% 25%;
    
    --accent: 320 70% 75%;
    --accent-foreground: 225 25% 25%;
    
    --voice-alex: 140 60% 60%;
    --voice-jordan: 30 70% 65%;
    --voice-taylor: 280 65% 70%;
    
    --user-voice: 200 60% 65%;
    
    --muted: 225 15% 92%;
    --muted-foreground: 225 15% 55%;
    
    --destructive: 0 75% 60%;
    --destructive-foreground: 0 0% 100%;
    
    --border: 225 15% 88%;
    --input: 225 15% 92%;
    --ring: 260 60% 65%;
    
    --radius: 1rem;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-glow)));
    --gradient-bg: linear-gradient(135deg, hsl(var(--neu-bg)), hsl(var(--neu-highlight)));
    --gradient-card: linear-gradient(145deg, hsl(var(--neu-highlight)), hsl(var(--neu-bg)));
    
    /* Shadows for Neumorphism */
    --shadow-neu-inset: inset 8px 8px 16px hsl(var(--neu-shadow)), inset -8px -8px 16px hsl(var(--neu-highlight));
    --shadow-neu-outset: 8px 8px 16px hsl(var(--neu-shadow)), -8px -8px 16px hsl(var(--neu-highlight));
    --shadow-glow: 0 0 40px hsl(var(--primary-glow) / 0.3);
    
    /* Animations */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* Neumorphic Dark Theme */
    --background: 225 25% 15%;
    --foreground: 225 15% 85%;
    
    --neu-bg: 225 25% 15%;
    --neu-highlight: 225 20% 18%;
    --neu-shadow: 225 30% 12%;
    --neu-border: 225 20% 20%;
    
    --card: 225 25% 15%;
    --card-foreground: 225 15% 85%;
    
    --primary: 260 70% 70%;
    --primary-foreground: 225 25% 15%;
    --primary-glow: 260 80% 80%;
    
    --secondary: 200 60% 75%;
    --secondary-foreground: 225 25% 15%;
    
    --accent: 320 80% 80%;
    --accent-foreground: 225 25% 15%;
    
    --voice-alex: 140 70% 65%;
    --voice-jordan: 30 80% 70%;
    --voice-taylor: 280 75% 75%;
    
    --user-voice: 200 70% 70%;
    
    --muted: 225 20% 20%;
    --muted-foreground: 225 15% 65%;
    
    --destructive: 0 85% 65%;
    --destructive-foreground: 225 25% 15%;
    
    --border: 225 20% 22%;
    --input: 225 20% 18%;
    --ring: 260 70% 70%;
    
    /* Dark Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-glow)));
    --gradient-bg: linear-gradient(135deg, hsl(var(--neu-bg)), hsl(var(--neu-highlight)));
    --gradient-card: linear-gradient(145deg, hsl(var(--neu-highlight)), hsl(var(--neu-bg)));
    
    /* Dark Shadows */
    --shadow-neu-inset: inset 8px 8px 16px hsl(var(--neu-shadow)), inset -8px -8px 16px hsl(var(--neu-highlight));
    --shadow-neu-outset: 8px 8px 16px hsl(var(--neu-shadow)), -8px -8px 16px hsl(var(--neu-highlight));
    --shadow-glow: 0 0 50px hsl(var(--primary-glow) / 0.4);
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-gradient-to-br from-neu-bg to-neu-highlight text-foreground;
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
  }
  
  /* Custom Scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }
  
  ::-webkit-scrollbar-track {
    background: hsl(var(--neu-bg));
  }
  
  ::-webkit-scrollbar-thumb {
    background: hsl(var(--primary) / 0.3);
    border-radius: 4px;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--primary) / 0.5);
  }
}

/* Neumorphic Components */
@layer components {
  .neu-card {
    @apply rounded-2xl shadow-[var(--shadow-neu-outset)] bg-gradient-to-br from-neu-highlight to-neu-bg;
  }
  
  .neu-inset {
    @apply rounded-xl shadow-[var(--shadow-neu-inset)] bg-gradient-to-br from-neu-bg to-neu-highlight;
  }
  
  .voice-glow {
    @apply transition-all duration-300 ease-out;
    box-shadow: var(--shadow-glow);
  }
  
  .speaking-pulse {
    animation: pulse-glow 1.5s ease-in-out infinite;
  }
  
  .glass-effect {
    @apply backdrop-blur-md bg-background/20 border border-border/30;
  }
}

/* Keyframes */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px hsl(var(--primary-glow) / 0.3);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 40px hsl(var(--primary-glow) / 0.6);
    transform: scale(1.02);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-slide-up {
  animation: slideInUp 0.5s ease-out;
}