import { useState } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>bulb, ArrowRight, Sparkles } from 'lucide-react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';

interface TopicSetupProps {
  onTopicSubmit: (topic: string) => void;
  isLoading?: boolean;
}

const SUGGESTED_TOPICS = [
  "The future of artificial intelligence in education",
  "Sustainable energy solutions for developing countries", 
  "The impact of social media on mental health",
  "Remote work vs traditional office environments",
  "The ethics of genetic engineering",
  "Climate change mitigation strategies",
  "The role of cryptocurrency in the global economy",
  "Space exploration funding priorities"
];

export function TopicSetup({ onTopicSubmit, isLoading = false }: TopicSetupProps) {
  const [customTopic, setCustomTopic] = useState('');

  const handleSubmit = (topic: string) => {
    if (topic.trim()) {
      onTopicSubmit(topic.trim());
    }
  };

  const handleCustomSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSubmit(customTopic);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="max-w-4xl mx-auto"
    >
      {/* Header */}
      <div className="text-center mb-8">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2, type: "spring" }}
          className="inline-flex items-center justify-center w-20 h-20 neu-card rounded-full bg-gradient-primary text-primary-foreground mb-6"
        >
          <Sparkles className="h-10 w-10" />
        </motion.div>
        
        <motion.h1
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="text-4xl font-bold mb-4 bg-gradient-primary bg-clip-text text-transparent"
        >
          Voice Verse Simulator
        </motion.h1>
        
        <motion.p
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="text-xl text-muted-foreground mb-8"
        >
          Join an AI-powered group discussion with three unique characters
        </motion.p>
      </div>

      {/* Custom Topic Input */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
      >
        <Card className="neu-card mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Lightbulb className="h-5 w-5 text-primary" />
              Choose Your Discussion Topic
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleCustomSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="topic">What would you like to discuss?</Label>
                <div className="flex gap-3">
                  <Input
                    id="topic"
                    placeholder="Enter a topic for discussion..."
                    value={customTopic}
                    onChange={(e) => setCustomTopic(e.target.value)}
                    className="neu-inset flex-1"
                    disabled={isLoading}
                  />
                  <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                    <Button
                      type="submit"
                      disabled={!customTopic.trim() || isLoading}
                      className="neu-card bg-gradient-primary text-primary-foreground hover:shadow-glow"
                    >
                      Start Discussion
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </Button>
                  </motion.div>
                </div>
              </div>
            </form>
          </CardContent>
        </Card>
      </motion.div>

      {/* Suggested Topics */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
      >
        <Card className="neu-card">
          <CardHeader>
            <CardTitle className="text-lg">Or Choose a Suggested Topic</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {SUGGESTED_TOPICS.map((topic, index) => (
                <motion.div
                  key={topic}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.7 + index * 0.1 }}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Button
                    variant="outline"
                    onClick={() => handleSubmit(topic)}
                    disabled={isLoading}
                    className="neu-inset w-full h-auto p-4 text-left justify-start hover:bg-primary/5 hover:border-primary/30"
                  >
                    <div>
                      <p className="font-medium text-sm leading-tight">{topic}</p>
                    </div>
                  </Button>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Characters Preview */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}
        className="mt-8"
      >
        <Card className="neu-card">
          <CardHeader>
            <CardTitle>Meet Your Discussion Partners</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <motion.div
                whileHover={{ y: -5 }}
                className="text-center p-4 neu-inset rounded-xl"
              >
                <div className="text-4xl mb-3">😊</div>
                <h3 className="font-semibold text-voice-alex">Alex</h3>
                <p className="text-sm text-muted-foreground mb-2">Optimistic Leader</p>
                <Badge variant="secondary" className="text-xs">Enthusiastic & Motivational</Badge>
              </motion.div>

              <motion.div
                whileHover={{ y: -5 }}
                className="text-center p-4 neu-inset rounded-xl"
              >
                <div className="text-4xl mb-3">🤔</div>
                <h3 className="font-semibold text-voice-jordan">Jordan</h3>
                <p className="text-sm text-muted-foreground mb-2">Skeptical Analyst</p>
                <Badge variant="secondary" className="text-xs">Critical & Question-focused</Badge>
              </motion.div>

              <motion.div
                whileHover={{ y: -5 }}
                className="text-center p-4 neu-inset rounded-xl"
              >
                <div className="text-4xl mb-3">✨</div>
                <h3 className="font-semibold text-voice-taylor">Taylor</h3>
                <p className="text-sm text-muted-foreground mb-2">Creative Visionary</p>
                <Badge variant="secondary" className="text-xs">Innovative & Creative</Badge>
              </motion.div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  );
}