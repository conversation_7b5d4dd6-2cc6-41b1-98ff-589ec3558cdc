# 🎤 Voice Verse - AI Group Discussion Simulator

**A beautiful, real-time voice-based group discussion simulator powered by Gemini AI and ElevenLabs text-to-speech technology.**

![Voice Verse Preview](https://images.unsplash.com/photo-1614064641938-3bbee52942c7?w=800&h=400&fit=crop)

## ✨ Features

### 🤖 Three Unique AI Characters
- **Alex** 😊 - *Optimistic Leader*: Enthusiastic and motivational, always sees the bright side
- **Jordan** 🤔 - *Skeptical Analyst*: Critical thinker who asks tough questions and focuses on risks  
- **Taylor** ✨ - *Creative Visionary*: Innovative mind that thinks outside the box with wild ideas

### 🎙️ Advanced Voice Technology
- **Real-time Speech Recognition**: Browser-based voice input using Web Audio API
- **Natural Text-to-Speech**: High-quality voice synthesis via ElevenLabs
- **Low-latency Audio**: Optimized for smooth conversation flow
- **Voice Activity Detection**: Visual cues for active speakers

### 🎨 Beautiful Design
- **Neumorphic UI**: Modern soft-shadow design system
- **Smooth Animations**: Framer Motion powered transitions and interactions
- **Dark/Light Mode**: Automatic theme switching with beautiful gradients
- **Responsive Layout**: Perfect on desktop, tablet, and mobile devices
- **Visual Feedback**: Animated avatars, speech bubbles, and status indicators

### 🧠 Intelligent Conversations
- **Context-Aware AI**: Characters remember and build upon previous discussion points
- **Natural Turn-Taking**: Smart conversation flow with realistic interruptions
- **Topic Flexibility**: Discuss anything from technology to philosophy
- **User Integration**: Raise your hand to join the conversation naturally

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm
- [Gemini API Key](https://makersuite.google.com/app/apikey)
- [ElevenLabs API Key](https://elevenlabs.io/app/speech-synthesis)

### Installation

```bash
# Clone the repository
git clone <YOUR_GIT_URL>
cd voice-verse-sim

# Install dependencies
npm install

# Start the development server
npm run dev
```

### Configuration

1. **Open the application** in your browser (usually http://localhost:8080)
2. **Click "Configure API Keys"** in the header
3. **Enter your API keys**:
   - Gemini API key for AI conversations
   - ElevenLabs API key for voice synthesis
4. **Grant microphone permissions** when prompted
5. **Choose a discussion topic** and start your first conversation!

## 🎯 How to Use

### Starting a Discussion
1. **Choose a Topic**: Select from suggested topics or enter your own
2. **Watch the AI Characters**: They'll begin discussing automatically
3. **Raise Your Hand**: Click the hand button when you want to contribute
4. **Speak Your Mind**: Use the microphone to add your thoughts
5. **Enjoy the Flow**: Characters will respond to your input naturally

### Voice Controls
- 🎤 **Microphone**: Record your voice input (only when hand is raised)
- ✋ **Raise Hand**: Signal that you want to join the conversation
- ⏯️ **Play/Pause**: Control the automatic discussion flow
- ⚙️ **Settings**: Configure API keys and permissions

### Pro Tips
- **Speak Clearly**: Ensure good audio quality for better transcription
- **Wait for Silence**: Let characters finish before speaking
- **Be Natural**: Talk as you would in a real group discussion
- **Try Different Topics**: Explore various subjects to see character personalities

## 🛠️ Technology Stack

### Frontend
- **React 18** with TypeScript for robust component architecture
- **Vite** for lightning-fast development and building
- **Tailwind CSS** with custom neumorphic design system
- **Framer Motion** for smooth animations and transitions
- **shadcn/ui** for beautiful, accessible UI components

### AI & Voice
- **Google Gemini** (Gemini 1.5 Flash) for intelligent conversation generation
- **ElevenLabs** for high-quality text-to-speech synthesis
- **Web Speech API** for browser-based speech recognition
- **Web Audio API** for advanced audio processing

### Key Libraries
- `@google/generative-ai` - Gemini AI integration
- `@11labs/react` - ElevenLabs TTS SDK
- `react-speech-recognition` - Speech recognition utilities
- `framer-motion` - Animation library
- `next-themes` - Theme management

## 🔧 API Configuration

### Gemini AI Setup
1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Ensure you have access to Gemini models
4. Add the key to Voice Verse settings

### ElevenLabs Setup
1. Sign up at [ElevenLabs](https://elevenlabs.io)
2. Navigate to your [API keys page](https://elevenlabs.io/app/speech-synthesis)
3. Generate a new API key
4. Add the key to Voice Verse settings

### Voice IDs Used
- **Alex**: `EXAVITQu4vr4xnSDxMaL` (Sarah voice)
- **Jordan**: `MF3mGyEYCl7XYWbV9V6O` (Custom voice)
- **Taylor**: `21m00Tcm4TlvDq8ikWAM` (Rachel voice)

## 🎨 Customization

### Adding New Characters
1. Edit `src/types/voice.ts` to add character definitions
2. Update the AI service prompts in `src/services/aiService.ts`
3. Add corresponding voice IDs and visual styling

### Modifying Conversation Logic
- **Turn-taking rules**: `src/services/aiService.ts`
- **Voice timing**: `src/hooks/useVoiceDiscussion.ts`
- **UI animations**: Component files in `src/components/voice/`

### Theming
- **Colors**: `src/index.css` (HSL color system)
- **Neumorphic shadows**: CSS custom properties
- **Animations**: Tailwind config and Framer Motion variants

## 🚨 Troubleshooting

### Common Issues

**"Speech recognition not working"**
- Ensure you're using Chrome, Firefox, or Safari
- Check microphone permissions in browser settings
- Try speaking more clearly and closer to the microphone

**"API key validation failed"**
- Verify your API keys are correct and active
- Check your billing status on both Gemini and ElevenLabs
- Ensure proper network connectivity

**"Audio not playing"**
- Check browser audio permissions
- Verify ElevenLabs API key and credits
- Try refreshing the page and re-entering keys

**"Characters not responding"**
- Confirm Gemini API key is valid and has quota
- Check browser console for error messages
- Try a simpler discussion topic

### Browser Compatibility
- **Recommended**: Chrome 90+, Firefox 88+, Safari 14+
- **Speech Recognition**: Chrome/Edge (best), Firefox (limited), Safari (limited)
- **Audio Playback**: All modern browsers supported

## 📱 Mobile Support

Voice Verse works on mobile devices with some limitations:
- Touch-optimized controls for all interactions
- Speech recognition availability varies by mobile browser
- Optimized for both portrait and landscape orientations
- Data usage considerations for voice synthesis

## 🔒 Privacy & Security

- **Local Storage**: API keys stored locally in browser only
- **No Data Collection**: Conversations are not saved or transmitted
- **Secure APIs**: Direct communication with AI services
- **Microphone Access**: Used only when explicitly granted and activated

## 🤝 Contributing

We welcome contributions! Areas where you can help:
- Additional character personalities and voices
- New discussion topics and conversation starters
- UI/UX improvements and animations
- Mobile experience optimization
- Accessibility enhancements

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- **ElevenLabs** for incredible voice synthesis technology
- **Google** for powerful Gemini AI models
- **shadcn** for beautiful UI components
- **The React community** for amazing tools and libraries

---

**Built with ❤️ using Lovable - Create beautiful AI-powered applications**

[🌟 Try Voice Verse Live](https://lovable.dev/projects/f81c8fff-21bc-4bc3-8cd0-d67d4e468ea5) | [📖 Documentation](https://docs.lovable.dev) | [🐛 Report Issues](https://github.com/lovable-dev/voice-verse-sim/issues)