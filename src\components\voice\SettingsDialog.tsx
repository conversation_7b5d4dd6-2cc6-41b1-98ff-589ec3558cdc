import { useState } from 'react';
import { motion } from 'framer-motion';
import { Key, Mic, Volume2, Eye, EyeOff } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ApiKeys } from '@/types/voice';
import { useToast } from '@/hooks/use-toast';

interface SettingsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  apiKeys: ApiKeys;
  onApiKeysChange: (keys: ApiKeys) => void;
  onRequestMicPermission: () => Promise<boolean>;
  micPermissionGranted: boolean;
}

export function SettingsDialog({
  open,
  onOpenChange,
  apiKeys,
  onApiKeysChange,
  onRequestMicPermission,
  micPermissionGranted
}: SettingsDialogProps) {
  const [localKeys, setLocalKeys] = useState(apiKeys);
  const [showKeys, setShowKeys] = useState({ gemini: false, elevenLabs: false });
  const [isValidating, setIsValidating] = useState(false);
  const { toast } = useToast();

  const handleSave = async () => {
    if (!localKeys.geminiKey || !localKeys.elevenLabsKey) {
      toast({
        title: "Missing API Keys",
        description: "Please provide both Gemini and ElevenLabs API keys.",
        variant: "destructive"
      });
      return;
    }

    setIsValidating(true);
    
    try {
      // Validate Gemini API key
      const geminiResponse = await fetch('https://generativelanguage.googleapis.com/v1beta/models?key=' + localKeys.geminiKey);
      if (!geminiResponse.ok) {
        throw new Error('Invalid Gemini API key');
      }

      // Validate ElevenLabs API key
      const elevenLabsResponse = await fetch('https://api.elevenlabs.io/v1/voices', {
        headers: { 'xi-api-key': localKeys.elevenLabsKey }
      });
      if (!elevenLabsResponse.ok) {
        throw new Error('Invalid ElevenLabs API key');
      }

      onApiKeysChange(localKeys);
      toast({
        title: "API Keys Validated",
        description: "Your API keys have been validated and saved successfully.",
      });
      onOpenChange(false);
    } catch (error) {
      toast({
        title: "Validation Failed",
        description: error instanceof Error ? error.message : "Failed to validate API keys",
        variant: "destructive"
      });
    } finally {
      setIsValidating(false);
    }
  };

  const handleMicPermission = async () => {
    const granted = await onRequestMicPermission();
    if (granted) {
      toast({
        title: "Microphone Access Granted",
        description: "You can now use voice input in conversations.",
      });
    } else {
      toast({
        title: "Microphone Access Denied",
        description: "Please enable microphone access in your browser settings.",
        variant: "destructive"
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="neu-card max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Key className="h-5 w-5" />
            Voice Verse Settings
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* API Keys Section */}
          <Card className="neu-inset">
            <CardHeader>
              <CardTitle className="text-lg">API Configuration</CardTitle>
              <CardDescription>
                Configure your Gemini and ElevenLabs API keys for AI conversations and voice synthesis.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="gemini-key">Gemini API Key</Label>
                <div className="relative">
                  <Input
                    id="gemini-key"
                    type={showKeys.gemini ? "text" : "password"}
                    placeholder="Enter your Gemini API key..."
                    value={localKeys.geminiKey}
                    onChange={(e) => setLocalKeys(prev => ({ ...prev, geminiKey: e.target.value }))}
                    className="neu-inset pr-10"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3"
                    onClick={() => setShowKeys(prev => ({ ...prev, gemini: !prev.gemini }))}
                  >
                    {showKeys.gemini ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="elevenlabs-key">ElevenLabs API Key</Label>
                <div className="relative">
                  <Input
                    id="elevenlabs-key"
                    type={showKeys.elevenLabs ? "text" : "password"}
                    placeholder="Enter your ElevenLabs API key..."
                    value={localKeys.elevenLabsKey}
                    onChange={(e) => setLocalKeys(prev => ({ ...prev, elevenLabsKey: e.target.value }))}
                    className="neu-inset pr-10"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3"
                    onClick={() => setShowKeys(prev => ({ ...prev, elevenLabs: !prev.elevenLabs }))}
                  >
                    {showKeys.elevenLabs ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Microphone Permission */}
          <Card className="neu-inset">
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Mic className="h-5 w-5" />
                Microphone Access
              </CardTitle>
              <CardDescription>
                Grant microphone access to enable voice input during conversations.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className={`w-3 h-3 rounded-full ${micPermissionGranted ? 'bg-green-500' : 'bg-red-500'}`} />
                  <span className="text-sm">
                    {micPermissionGranted ? 'Microphone access granted' : 'Microphone access required'}
                  </span>
                </div>
                <Button
                  onClick={handleMicPermission}
                  disabled={micPermissionGranted}
                  variant={micPermissionGranted ? "secondary" : "default"}
                  className="neu-card"
                >
                  <Volume2 className="h-4 w-4 mr-2" />
                  {micPermissionGranted ? 'Granted' : 'Grant Access'}
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3">
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
              className="neu-card"
            >
              Cancel
            </Button>
            <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
              <Button
                onClick={handleSave}
                disabled={isValidating}
                className="neu-card bg-gradient-primary text-primary-foreground hover:shadow-glow"
              >
                {isValidating ? 'Validating...' : 'Save & Validate'}
              </Button>
            </motion.div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}