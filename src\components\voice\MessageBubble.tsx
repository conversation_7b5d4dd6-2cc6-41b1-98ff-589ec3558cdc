import { motion } from "framer-motion";
import { Message, Character } from "@/types/voice";
import { CharacterAvatar } from "./CharacterAvatar";
import { cn } from "@/lib/utils";

interface MessageBubbleProps {
	message: Message;
	character?: Character;
	isPlaying?: boolean;
}

export function MessageBubble({
	message,
	character,
	isPlaying = false,
}: MessageBubbleProps) {
	const isUser = message.isUser;

	return (
		<motion.div
			initial={{ opacity: 0, y: 20, scale: 0.9 }}
			animate={{ opacity: 1, y: 0, scale: 1 }}
			transition={{ duration: 0.4, ease: "easeOut" }}
			className={cn(
				"flex items-start gap-4 p-4 group",
				isUser ? "flex-row-reverse" : "flex-row"
			)}
		>
			<div className="flex-shrink-0">
				{character ? (
					<CharacterAvatar
						character={character}
						isSpeaking={isPlaying}
						size="md"
					/>
				) : (
					<div className="w-12 h-12 rounded-full flex items-center justify-center bg-gradient-to-br from-blue-500 to-blue-600 text-white font-bold text-lg shadow-lg">
						👤
					</div>
				)}
			</div>

			<motion.div
				className={cn(
					"relative rounded-2xl p-5 max-w-xs lg:max-w-lg xl:max-w-xl shadow-lg border transition-all duration-300",
					isUser
						? "bg-gradient-to-br from-blue-500 to-blue-600 text-white border-blue-400 shadow-blue-500/20"
						: character
						? getCharacterBubbleStyle(character.id)
						: "bg-gradient-to-br from-gray-100 to-gray-200 text-gray-800 border-gray-300 dark:from-gray-700 dark:to-gray-600 dark:text-gray-100 dark:border-gray-500",
					isPlaying && "ring-2 ring-offset-2 ring-primary/50 scale-[1.02]",
					"group-hover:shadow-xl group-hover:scale-[1.01]"
				)}
				animate={
					isPlaying
						? {
								boxShadow: [
									"0 10px 25px rgba(0,0,0,0.1)",
									"0 15px 35px rgba(0,0,0,0.15)",
									"0 10px 25px rgba(0,0,0,0.1)",
								],
						  }
						: {}
				}
				transition={{ duration: 2, repeat: isPlaying ? Infinity : 0 }}
			>
				{/* Message Header */}
				<div className="flex items-center justify-between mb-3">
					<div className="flex items-center gap-2">
						<span
							className={cn(
								"font-bold text-base",
								isUser ? "text-white" : "text-gray-800 dark:text-gray-100"
							)}
						>
							{isUser ? "You" : character?.name}
						</span>
						{character && (
							<span
								className={cn(
									"text-xs px-2 py-1 rounded-full font-medium",
									isUser
										? "bg-white/20 text-white"
										: "bg-black/10 text-gray-600 dark:bg-white/10 dark:text-gray-300"
								)}
							>
								{character.role}
							</span>
						)}
					</div>
					{isPlaying && (
						<div className="flex items-center gap-1">
							<div className="w-2 h-2 bg-current rounded-full animate-pulse"></div>
							<div
								className="w-2 h-2 bg-current rounded-full animate-pulse"
								style={{ animationDelay: "0.2s" }}
							></div>
							<div
								className="w-2 h-2 bg-current rounded-full animate-pulse"
								style={{ animationDelay: "0.4s" }}
							></div>
						</div>
					)}
				</div>

				{/* Message Content */}
				<p
					className={cn(
						"text-base leading-relaxed mb-3",
						isUser ? "text-white" : "text-gray-700 dark:text-gray-200"
					)}
				>
					{message.content}
				</p>

				{/* Timestamp */}
				<div className="flex justify-end">
					<span
						className={cn(
							"text-xs font-medium",
							isUser ? "text-white/70" : "text-gray-500 dark:text-gray-400"
						)}
					>
						{message.timestamp.toLocaleTimeString([], {
							hour: "2-digit",
							minute: "2-digit",
						})}
					</span>
				</div>

				{/* Speech bubble tail */}
				<div
					className={cn(
						"absolute top-4 w-0 h-0",
						isUser
							? "right-full border-r-[12px] border-r-blue-500 border-t-[8px] border-t-transparent border-b-[8px] border-b-transparent"
							: "left-full border-l-[12px] border-t-[8px] border-t-transparent border-b-[8px] border-b-transparent",
						character && !isUser && getCharacterTailStyle(character.id)
					)}
				/>
			</motion.div>
		</motion.div>
	);
}

// Helper function to get character-specific bubble styles
function getCharacterBubbleStyle(characterId: string): string {
	switch (characterId) {
		case "alex":
			return "bg-gradient-to-br from-emerald-100 to-emerald-200 text-emerald-800 border-emerald-300 shadow-emerald-500/20 dark:from-emerald-800 dark:to-emerald-700 dark:text-emerald-100 dark:border-emerald-600";
		case "jordan":
			return "bg-gradient-to-br from-amber-100 to-amber-200 text-amber-800 border-amber-300 shadow-amber-500/20 dark:from-amber-800 dark:to-amber-700 dark:text-amber-100 dark:border-amber-600";
		case "taylor":
			return "bg-gradient-to-br from-purple-100 to-purple-200 text-purple-800 border-purple-300 shadow-purple-500/20 dark:from-purple-800 dark:to-purple-700 dark:text-purple-100 dark:border-purple-600";
		default:
			return "bg-gradient-to-br from-gray-100 to-gray-200 text-gray-800 border-gray-300 dark:from-gray-700 dark:to-gray-600 dark:text-gray-100 dark:border-gray-500";
	}
}

// Helper function to get character-specific tail styles
function getCharacterTailStyle(characterId: string): string {
	switch (characterId) {
		case "alex":
			return "border-l-emerald-100 dark:border-l-emerald-800";
		case "jordan":
			return "border-l-amber-100 dark:border-l-amber-800";
		case "taylor":
			return "border-l-purple-100 dark:border-l-purple-800";
		default:
			return "border-l-gray-100 dark:border-l-gray-700";
	}
}
