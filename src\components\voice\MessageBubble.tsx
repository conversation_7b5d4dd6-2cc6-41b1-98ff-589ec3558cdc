import { motion } from 'framer-motion';
import { Message, Character } from '@/types/voice';
import { CharacterAvatar } from './CharacterAvatar';
import { cn } from '@/lib/utils';

interface MessageBubbleProps {
  message: Message;
  character?: Character;
  isPlaying?: boolean;
}

export function MessageBubble({ message, character, isPlaying = false }: MessageBubbleProps) {
  const isUser = message.isUser;
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20, scale: 0.9 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
      className={cn(
        "flex items-start gap-3 p-4",
        isUser ? "flex-row-reverse" : "flex-row"
      )}
    >
      <div className="flex-shrink-0">
        {character ? (
          <CharacterAvatar 
            character={character} 
            isSpeaking={isPlaying}
            size="sm"
          />
        ) : (
          <div className="w-10 h-10 neu-card rounded-full flex items-center justify-center bg-user-voice/20 text-user-voice font-bold">
            👤
          </div>
        )}
      </div>
      
      <motion.div
        className={cn(
          "neu-card p-4 max-w-xs lg:max-w-md",
          isUser ? "bg-user-voice/10" : character ? `bg-${character.color}/10` : "bg-muted/50",
          isPlaying && "voice-glow"
        )}
        animate={isPlaying ? {
          boxShadow: [
            '0 0 20px hsl(var(--primary-glow) / 0.2)',
            '0 0 30px hsl(var(--primary-glow) / 0.4)',
            '0 0 20px hsl(var(--primary-glow) / 0.2)'
          ]
        } : {}}
        transition={{ duration: 2, repeat: isPlaying ? Infinity : 0 }}
      >
        <div className="flex items-center gap-2 mb-2">
          <span className="font-semibold text-sm">
            {isUser ? 'You' : character?.name}
          </span>
          {character && (
            <span className="text-xs text-muted-foreground">
              {character.role}
            </span>
          )}
        </div>
        <p className="text-sm leading-relaxed">{message.content}</p>
        <span className="text-xs text-muted-foreground mt-2 block">
          {message.timestamp.toLocaleTimeString()}
        </span>
      </motion.div>
    </motion.div>
  );
}