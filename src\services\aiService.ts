import { GoogleGenerativeAI } from '@google/generative-ai';
import { CHARACTERS, Character, Message } from '../types/voice';

export class AIService {
  private genAI: GoogleGenerativeAI;
  
  constructor(apiKey: string) {
    this.genAI = new GoogleGenerativeAI(apiKey);
  }

  async generateConversation(
    topic: string,
    conversationHistory: Message[],
    userMessage?: string
  ): Promise<{ characterId: string; content: string }[]> {
    try {
      const model = this.genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });
      
      const systemPrompt = this.buildSystemPrompt(topic, conversationHistory, userMessage);
      
      const result = await model.generateContent(systemPrompt);
      const response = await result.response;
      const text = response.text();
      
      return this.parseAIResponse(text);
    } catch (error) {
      console.error('AI Service Error:', error);
      throw new Error('Failed to generate conversation');
    }
  }

  private buildSystemPrompt(
    topic: string, 
    history: Message[], 
    userMessage?: string
  ): string {
    const historyText = history
      .slice(-10) // Last 10 messages for context
      .map(msg => {
        const character = CHARACTERS.find(c => c.id === msg.characterId);
        const name = msg.isUser ? 'User' : character?.name || 'Unknown';
        return `${name}: ${msg.content}`;
      })
      .join('\n');

    const characterPersonas = {
      alex: "Alex speaks with enthusiasm and optimism, using phrases like 'That's fantastic!', 'I love where this is going!', 'What if we...', 'This could be amazing!'. Focuses on possibilities and team building.",
      jordan: "Jordan is analytical and critical, asking probing questions like 'But what about...?', 'Have we considered...?', 'I'm concerned that...', 'The data suggests...'. Always looks for potential issues.",
      taylor: "Taylor is creative and innovative, using phrases like 'What if we flip this?', 'I'm imagining...', 'This reminds me of...', 'We could totally revolutionize...'. Thinks outside the box."
    };

    return `
You are managing a group discussion with 3 distinct AI characters about "${topic}".

Character Details:
- Alex (Optimistic Leader): ${characterPersonas.alex}
- Jordan (Skeptical Analyst): ${characterPersonas.jordan}  
- Taylor (Creative Visionary): ${characterPersonas.taylor}

CRITICAL RULES:
1. Generate responses for 1-2 characters only (not all 3 every time)
2. Each character must speak differently - avoid repetitive phrases
3. Keep responses under 40 words, natural and conversational
4. Format EXACTLY as: "CHARACTER_ID: message content"
5. Use IDs: alex, jordan, taylor
6. Make characters build on each other's ideas or challenge them
7. Vary who speaks - don't always use the same character order
${userMessage ? `8. IMPORTANT: The user just contributed: "${userMessage}" - characters should directly respond to this input` : '8. Continue the natural discussion flow'}

Recent conversation:
${historyText}

Generate the next natural responses (1-2 characters):`;
  }

  private parseAIResponse(response: string): { characterId: string; content: string }[] {
    const lines = response.split('\n').filter(line => line.trim());
    const results: { characterId: string; content: string }[] = [];
    
    for (const line of lines) {
      const match = line.match(/^(alex|jordan|taylor):\s*(.+)$/i);
      if (match) {
        const [, characterId, content] = match;
        results.push({
          characterId: characterId.toLowerCase(),
          content: content.trim()
        });
      }
    }
    
    // Fallback if parsing fails
    if (results.length === 0) {
      results.push({
        characterId: 'alex',
        content: "Let's continue our discussion about this interesting topic!"
      });
    }
    
    return results;
  }
}