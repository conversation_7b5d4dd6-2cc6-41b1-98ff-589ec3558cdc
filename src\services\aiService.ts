import { GoogleGenerativeAI } from "@google/generative-ai";
import { CHARACTERS, Character, Message } from "../types/voice";

export class AIService {
	private genAI: GoogleGenerativeAI;

	constructor(apiKey: string) {
		this.genAI = new GoogleGenerativeAI(apiKey);
	}

	async generateConversation(
		topic: string,
		conversationHistory: Message[],
		userMessage?: string
	): Promise<{ characterId: string; content: string }[]> {
		try {
			console.log("🧠 AI Service generating conversation:", {
				topic,
				historyLength: conversationHistory.length,
				userMessage,
				hasUserMessage: !!userMessage,
			});

			const model = this.genAI.getGenerativeModel({
				model: "gemini-1.5-flash",
			});

			const systemPrompt = this.buildSystemPrompt(
				topic,
				conversationHistory,
				userMessage
			);

			console.log("📝 System prompt:", systemPrompt);

			const result = await model.generateContent(systemPrompt);
			const response = await result.response;
			const text = response.text();

			console.log("🤖 Raw AI response:", text);

			const parsedResponses = this.parseAIResponse(text);
			console.log("✅ Parsed AI responses:", parsedResponses);

			return parsedResponses;
		} catch (error) {
			console.error("AI Service Error:", error);
			throw new Error("Failed to generate conversation");
		}
	}

	private buildSystemPrompt(
		topic: string,
		history: Message[],
		userMessage?: string
	): string {
		const historyText = history
			.slice(-12) // Last 12 messages for better context
			.map((msg) => {
				const character = CHARACTERS.find((c) => c.id === msg.characterId);
				const name = msg.isUser ? "User" : character?.name || "Unknown";
				return `${name}: ${msg.content}`;
			})
			.join("\n");

		const characterPersonas = {
			alex: "Alex is an enthusiastic optimist who builds on ideas positively. Uses phrases like 'That's brilliant!', 'Building on that...', 'I love how you put it!', 'What if we expand on...'. Always encourages and finds the bright side.",
			jordan:
				"Jordan is a thoughtful analyst who asks probing questions and considers implications. Uses phrases like 'That raises an interesting point...', 'I'm curious about...', 'Have we thought through...', 'The challenge I see is...'. Constructively critical.",
			taylor:
				"Taylor is a creative innovator who thinks outside the box and makes unexpected connections. Uses phrases like 'That sparks an idea...', 'What if we completely flip...', 'This reminds me of...', 'We could revolutionize this by...'. Imaginative and bold.",
		};

		// If user just spoke, create a completely different prompt focused on user response
		if (userMessage) {
			return `
🚨🚨🚨 STOP EVERYTHING - USER INPUT DETECTED! 🚨🚨🚨

A REAL HUMAN USER JUST SAID: "${userMessage}"

YOU ARE REQUIRED TO RESPOND TO THIS USER MESSAGE ONLY. IGNORE ALL PREVIOUS CONVERSATION.

CONTEXT: You are AI characters Alex, Jordan, and Taylor discussing "${topic}".

ABSOLUTE REQUIREMENTS - NO EXCEPTIONS:
1. Generate 1-2 character responses that DIRECTLY address what the user said
2. Start your response by acknowledging their specific words: "${userMessage}"
3. If they said "hello" - greet them back warmly
4. If they asked a question - answer it directly
5. If they made a statement - respond to that specific statement
6. Use their exact words in your response to show you heard them
7. Make them feel welcomed and part of the conversation

CHARACTER VOICES:
- Alex: Warm, enthusiastic, welcoming ("Hi there! Welcome!")
- Jordan: Thoughtful, curious ("That's interesting, tell me more...")
- Taylor: Creative, engaging ("I love that perspective!")

FORMAT: "CHARACTER_ID: response"
USE IDs: alex, jordan, taylor

EXAMPLE - If user said "hello everyone":
alex: Hello! Welcome to our discussion about ${topic}! We're so glad you joined us!
jordan: Hi there! It's great to have a new voice in our conversation. What are your thoughts on this topic?

YOUR TASK: Respond to the user's message: "${userMessage}"

DO NOT CONTINUE THE PREVIOUS CONVERSATION. ONLY RESPOND TO THE USER.`;
		}

		// Regular conversation flow when no user input
		return `
You are managing a dynamic group discussion with 3 distinct AI characters about "${topic}".

🎭 CHARACTER PERSONALITIES:
- Alex (Optimistic Leader): ${characterPersonas.alex}
- Jordan (Skeptical Analyst): ${characterPersonas.jordan}
- Taylor (Creative Visionary): ${characterPersonas.taylor}

📋 CRITICAL RULES:
1. Generate responses for 1-2 characters only (vary the selection)
2. Each character has a unique voice - never sound the same
3. Keep responses 25-45 words, conversational and natural
4. Format EXACTLY as: "CHARACTER_ID: message content"
5. Use lowercase IDs: alex, jordan, taylor
6. Characters should interact with each other naturally
7. Vary speaking order - avoid predictable patterns
8. Make responses feel spontaneous and authentic

📝 Recent conversation context:
${historyText}

Generate the next natural responses (1-2 characters who would logically continue the discussion):`;
	}

	private parseAIResponse(
		response: string
	): { characterId: string; content: string }[] {
		const lines = response.split("\n").filter((line) => line.trim());
		const results: { characterId: string; content: string }[] = [];

		for (const line of lines) {
			// Try multiple parsing patterns
			const patterns = [
				/^(alex|jordan|taylor):\s*(.+)$/i,
				/^(alex|jordan|taylor)\s*-\s*(.+)$/i,
				/^\*\*(alex|jordan|taylor)\*\*:\s*(.+)$/i,
				/^(alex|jordan|taylor)\s*says?:\s*(.+)$/i,
			];

			for (const pattern of patterns) {
				const match = line.match(pattern);
				if (match) {
					const [, characterId, content] = match;
					const cleanContent = content.trim().replace(/^["']|["']$/g, ""); // Remove quotes

					if (cleanContent.length > 0) {
						results.push({
							characterId: characterId.toLowerCase(),
							content: cleanContent,
						});
						break; // Found a match, move to next line
					}
				}
			}
		}

		// Enhanced fallback with variety
		if (results.length === 0) {
			console.warn("⚠️ AI parsing failed, using fallback response");
			const fallbackResponses = [
				{
					characterId: "alex",
					content: "Welcome to our discussion! What would you like to explore?",
				},
				{
					characterId: "jordan",
					content: "That's an interesting perspective. Could you tell us more?",
				},
				{
					characterId: "taylor",
					content: "I love new ideas! What's your take on this topic?",
				},
			];

			const randomFallback =
				fallbackResponses[Math.floor(Math.random() * fallbackResponses.length)];
			results.push(randomFallback);
		}

		// Limit to maximum 2 responses to avoid overwhelming
		return results.slice(0, 2);
	}
}
