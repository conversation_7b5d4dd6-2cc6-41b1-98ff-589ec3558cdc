import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { ThemeProvider } from "next-themes";
import { ThemeToggle } from "@/components/ui/theme-toggle";
import { TopicSetup } from "@/components/voice/TopicSetup";
import { DiscussionInterface } from "@/components/voice/DiscussionInterface";
import { SettingsDialog } from "@/components/voice/SettingsDialog";
import { useVoiceDiscussion } from "@/hooks/useVoiceDiscussion";

const Index = () => {
	const [showSettings, setShowSettings] = useState(false);
	const {
		conversation,
		apiKeys,
		isListening,
		micPermissionGranted,
		isLoading,
		isPaused,
		startConversation,
		stopConversation,
		toggleConversation,
		clearMessages,
		restartConversation,
		raiseHand,
		startListening,
		stopListening,
		handleApiKeysChange,
		requestMicPermission,
	} = useVoiceDiscussion();

	const hasApiKeys = apiKeys.geminiKey && apiKeys.elevenLabsKey;
	const showTopicSetup = !conversation.topic;

	return (
		<ThemeProvider attribute="class" defaultTheme="light" enableSystem>
			<div className="min-h-screen bg-gradient-bg">
				{/* Header */}
				<motion.header
					initial={{ opacity: 0, y: -20 }}
					animate={{ opacity: 1, y: 0 }}
					className="fixed top-0 left-0 right-0 z-50 glass-effect border-b"
				>
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 h-16 flex items-center justify-between">
						<motion.div
							initial={{ opacity: 0, x: -20 }}
							animate={{ opacity: 1, x: 0 }}
							className="flex items-center gap-3"
						>
							<div className="w-8 h-8 neu-card rounded-lg flex items-center justify-center bg-gradient-primary text-primary-foreground">
								<span className="font-bold">VV</span>
							</div>
							<h1 className="font-bold text-lg">Voice Verse</h1>
						</motion.div>

						<div className="flex items-center gap-3">
							<ThemeToggle />
							{!hasApiKeys && (
								<motion.button
									whileHover={{ scale: 1.05 }}
									whileTap={{ scale: 0.95 }}
									onClick={() => setShowSettings(true)}
									className="neu-card px-4 py-2 text-sm font-medium bg-primary/10 text-primary rounded-xl"
								>
									Setup Required
								</motion.button>
							)}
						</div>
					</div>
				</motion.header>

				{/* Main Content */}
				<main className="pt-24 pb-8 px-4 sm:px-6 lg:px-8">
					<div className="max-w-7xl mx-auto">
						<AnimatePresence mode="wait">
							{!hasApiKeys ? (
								<motion.div
									key="setup-required"
									initial={{ opacity: 0, scale: 0.9 }}
									animate={{ opacity: 1, scale: 1 }}
									exit={{ opacity: 0, scale: 0.9 }}
									className="text-center py-20"
								>
									<div className="neu-card p-8 max-w-md mx-auto">
										<div className="w-16 h-16 neu-card rounded-full flex items-center justify-center bg-primary/10 text-primary mx-auto mb-6">
											<span className="text-2xl">⚙️</span>
										</div>
										<h2 className="text-2xl font-bold mb-4">Setup Required</h2>
										<p className="text-muted-foreground mb-6">
											Please configure your API keys to start using Voice Verse.
										</p>
										<motion.button
											whileHover={{ scale: 1.05 }}
											whileTap={{ scale: 0.95 }}
											onClick={() => setShowSettings(true)}
											className="neu-card px-6 py-3 bg-gradient-primary text-primary-foreground rounded-xl font-medium hover:shadow-glow transition-all"
										>
											Configure API Keys
										</motion.button>
									</div>
								</motion.div>
							) : showTopicSetup ? (
								<TopicSetup
									key="topic-setup"
									onTopicSubmit={startConversation}
									isLoading={isLoading}
								/>
							) : (
								<DiscussionInterface
									key="discussion"
									conversation={conversation}
									isListening={isListening}
									currentSpeaker={conversation.currentSpeaker}
									onStartListening={startListening}
									onStopListening={stopListening}
									onRaiseHand={raiseHand}
									onToggleConversation={toggleConversation}
									onOpenSettings={() => setShowSettings(true)}
									onClearMessages={clearMessages}
									onRestart={restartConversation}
									onCopyTranscript={(text) => {
										if (!text) return;
										navigator.clipboard.writeText(text).catch(() => {});
									}}
									disabled={isLoading}
								/>
							)}
						</AnimatePresence>
					</div>
				</main>

				{/* Settings Dialog */}
				<SettingsDialog
					open={showSettings}
					onOpenChange={setShowSettings}
					apiKeys={apiKeys}
					onApiKeysChange={handleApiKeysChange}
					onRequestMicPermission={requestMicPermission}
					micPermissionGranted={micPermissionGranted}
				/>
			</div>
		</ThemeProvider>
	);
};

export default Index;
