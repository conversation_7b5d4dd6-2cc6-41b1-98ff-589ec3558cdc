export interface Character {
  id: string;
  name: string;
  role: string;
  personality: string;
  voiceId: string;
  color: string;
  avatar: string;
}

export interface Message {
  id: string;
  characterId: string;
  content: string;
  timestamp: Date;
  isUser: boolean;
  isPlaying?: boolean;
}

export interface ConversationState {
  topic: string;
  messages: Message[];
  isActive: boolean;
  currentSpeaker: string | null;
  userHasRaised: boolean;
}

export interface ApiKeys {
  geminiKey: string;
  elevenLabsKey: string;
}

export const CHARACTERS: Character[] = [
  {
    id: 'alex',
    name: '<PERSON>',
    role: 'Optimistic Leader',
    personality: 'enthusiastic, motivational, always sees the bright side, encourages team collaboration',
    voiceId: 'EXAVITQu4vr4xnSDxMaL',
    color: 'voice-alex',
    avatar: '😊'
  },
  {
    id: 'jordan', 
    name: '<PERSON>',
    role: 'Skeptical Analyst',
    personality: 'critical thinker, asks tough questions, focuses on potential problems and risks',
    voiceId: 'MF3mGyEYCl7XYWbV9V6O',
    color: 'voice-jordan',
    avatar: '🤔'
  },
  {
    id: 'taylor',
    name: '<PERSON>', 
    role: 'Creative Visionary',
    personality: 'innovative, thinks outside the box, proposes creative solutions and wild ideas',
    voiceId: '21m00Tcm4TlvDq8ikWAM',
    color: 'voice-taylor',
    avatar: '✨'
  }
];