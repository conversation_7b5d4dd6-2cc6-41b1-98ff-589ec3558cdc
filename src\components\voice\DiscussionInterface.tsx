import { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
	MessageSquare,
	Users,
	Clock,
	RotateCcw,
	Trash2,
	Copy,
} from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { MessageBubble } from "./MessageBubble";
import { VoiceControls } from "./VoiceControls";
import { CharacterAvatar } from "./CharacterAvatar";
import { ConversationState, Message, CHARACTERS } from "@/types/voice";
import { cn } from "@/lib/utils";

interface DiscussionInterfaceProps {
	conversation: ConversationState;
	isListening: boolean;
	currentSpeaker: string | null;
	onStartListening: () => void;
	onStopListening: () => void;
	onRaiseHand: () => void;
	onToggleConversation: () => void;
	onOpenSettings: () => void;
	onClearMessages?: () => void;
	onRestart?: () => void;
	onCopyTranscript?: (text: string) => void;
	disabled?: boolean;
}

export function DiscussionInterface({
	conversation,
	isListening,
	currentSpeaker,
	onStartListening,
	onStopListening,
	onRaiseHand,
	onToggleConversation,
	onOpenSettings,
	onClearMessages,
	onRestart,
	onCopyTranscript,
	disabled = false,
}: DiscussionInterfaceProps) {
	const scrollAreaRef = useRef<HTMLDivElement>(null);
	const [sessionDuration, setSessionDuration] = useState(0);

	// Auto-scroll to bottom when new messages arrive
	useEffect(() => {
		if (scrollAreaRef.current) {
			const scrollContainer = scrollAreaRef.current.querySelector(
				"[data-radix-scroll-area-viewport]"
			);
			if (scrollContainer) {
				scrollContainer.scrollTop = scrollContainer.scrollHeight;
			}
		}
	}, [conversation.messages]);

	// Session timer
	useEffect(() => {
		let interval: NodeJS.Timeout;
		if (conversation.isActive) {
			interval = setInterval(() => {
				setSessionDuration((prev) => prev + 1);
			}, 1000);
		}
		return () => clearInterval(interval);
	}, [conversation.isActive]);

	const formatDuration = (seconds: number) => {
		const mins = Math.floor(seconds / 60);
		const secs = seconds % 60;
		return `${mins}:${secs.toString().padStart(2, "0")}`;
	};

	const getCharacterFromMessage = (message: Message) => {
		return CHARACTERS.find((c) => c.id === message.characterId);
	};

	const buildTranscript = () => {
		return conversation.messages
			.map((m) => {
				const who = m.isUser
					? "You"
					: CHARACTERS.find((c) => c.id === m.characterId)?.name || "Unknown";
				return `[${m.timestamp.toLocaleTimeString()}] ${who}: ${m.content}`;
			})
			.join("\n");
	};

	return (
		<div className="flex flex-col h-full max-h-[calc(100vh-8rem)]">
			{/* Discussion Header */}
			<motion.div
				initial={{ opacity: 0, y: -20 }}
				animate={{ opacity: 1, y: 0 }}
				className="neu-card p-6 mb-6"
			>
				<div className="flex items-center justify-between mb-4">
					<div className="flex items-center gap-3">
						<MessageSquare className="h-6 w-6 text-primary" />
						<h2 className="text-xl font-bold">Voice Discussion</h2>
						<Badge
							variant={conversation.isActive ? "default" : "secondary"}
							className="neu-inset"
						>
							{conversation.isActive ? "Active" : "Paused"}
						</Badge>
					</div>

					<div className="flex items-center gap-4 text-sm text-muted-foreground">
						<div className="flex items-center gap-1">
							<Clock className="h-4 w-4" />
							{formatDuration(sessionDuration)}
						</div>
						<div className="flex items-center gap-1">
							<Users className="h-4 w-4" />
							{conversation.messages.length} messages
						</div>
					</div>
				</div>

				{/* Header Actions */}
				<div className="flex items-center justify-end gap-2">
					<button
						type="button"
						onClick={() => onCopyTranscript?.(buildTranscript())}
						disabled={conversation.messages.length === 0}
						className="neu-inset px-3 py-2 rounded-lg text-xs hover:bg-primary/5 disabled:opacity-50"
						title="Copy transcript"
					>
						<span className="inline-flex items-center gap-2">
							<Copy className="h-4 w-4" /> Copy
						</span>
					</button>
					<button
						type="button"
						onClick={onClearMessages}
						disabled={conversation.messages.length === 0 || disabled}
						className="neu-inset px-3 py-2 rounded-lg text-xs hover:bg-primary/5 disabled:opacity-50"
						title="Clear messages"
					>
						<span className="inline-flex items-center gap-2">
							<Trash2 className="h-4 w-4" /> Clear
						</span>
					</button>
					<button
						type="button"
						onClick={onRestart}
						disabled={!conversation.topic || disabled}
						className="neu-inset px-3 py-2 rounded-lg text-xs hover:bg-primary/5 disabled:opacity-50"
						title="Restart conversation"
					>
						<span className="inline-flex items-center gap-2">
							<RotateCcw className="h-4 w-4" /> Restart
						</span>
					</button>
				</div>

				{/* Topic Display */}
				<div className="neu-inset p-4 rounded-xl">
					<h3 className="font-semibold text-primary mb-2">Discussion Topic</h3>
					<p className="text-sm text-muted-foreground">{conversation.topic}</p>
				</div>

				{/* Character Status */}
				<div className="flex items-center justify-center gap-6 mt-6">
					{CHARACTERS.map((character) => (
						<motion.div
							key={character.id}
							className="flex flex-col items-center gap-2"
							whileHover={{ scale: 1.05 }}
						>
							<CharacterAvatar
								character={character}
								isActive={currentSpeaker === character.id}
								isSpeaking={currentSpeaker === character.id}
								size="md"
							/>
							<div className="text-center">
								<p className="font-medium text-sm">{character.name}</p>
								<p className="text-xs text-muted-foreground">
									{character.role}
								</p>
							</div>
						</motion.div>
					))}
				</div>
			</motion.div>

			{/* Messages Area */}
			<Card className="neu-card flex-1 flex flex-col min-h-0">
				<CardHeader className="pb-3">
					<CardTitle className="text-lg">Conversation</CardTitle>
				</CardHeader>
				<CardContent className="flex-1 min-h-0 p-4">
					<ScrollArea ref={scrollAreaRef} className="h-full">
						<AnimatePresence mode="popLayout">
							{conversation.messages.length === 0 ? (
								<motion.div
									initial={{ opacity: 0 }}
									animate={{ opacity: 1 }}
									className="flex flex-col items-center justify-center h-32 text-center"
								>
									<MessageSquare className="h-8 w-8 text-muted-foreground mb-2" />
									<p className="text-muted-foreground">
										The discussion will begin once you start the conversation...
									</p>
								</motion.div>
							) : (
								<div className="space-y-4 pb-4">
									{conversation.messages.map((message) => (
										<MessageBubble
											key={message.id}
											message={message}
											character={getCharacterFromMessage(message)}
											isPlaying={currentSpeaker === message.characterId}
										/>
									))}
								</div>
							)}
						</AnimatePresence>
					</ScrollArea>
				</CardContent>
			</Card>

			{/* Voice Controls */}
			<motion.div
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ delay: 0.2 }}
				className="mt-6"
			>
				<VoiceControls
					isListening={isListening}
					isConversationActive={conversation.isActive}
					userHasRaised={conversation.userHasRaised}
					onStartListening={onStartListening}
					onStopListening={onStopListening}
					onRaiseHand={onRaiseHand}
					onToggleConversation={onToggleConversation}
					onOpenSettings={onOpenSettings}
					disabled={disabled}
				/>
			</motion.div>

			{/* User Status Indicator */}
			<AnimatePresence>
				{conversation.userHasRaised && (
					<motion.div
						initial={{ opacity: 0, scale: 0.9, y: 10 }}
						animate={{ opacity: 1, scale: 1, y: 0 }}
						exit={{ opacity: 0, scale: 0.9, y: 10 }}
						className="mt-4 neu-card p-4 text-center bg-primary/10"
					>
						<p className="text-sm font-medium text-primary">
							{isListening
								? "🎤 Listening..."
								: "✋ Ready to speak - Click the microphone when you want to join"}
						</p>
					</motion.div>
				)}
			</AnimatePresence>
		</div>
	);
}
