import { motion } from 'framer-motion';
import { Character } from '@/types/voice';
import { cn } from '@/lib/utils';

interface CharacterAvatarProps {
  character: Character;
  isActive?: boolean;
  isSpeaking?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

export function CharacterAvatar({ 
  character, 
  isActive = false, 
  isSpeaking = false,
  size = 'md' 
}: CharacterAvatarProps) {
  const sizeClasses = {
    sm: 'w-10 h-10 text-lg',
    md: 'w-16 h-16 text-2xl',
    lg: 'w-20 h-20 text-3xl'
  };

  return (
    <motion.div
      className={cn(
        "neu-card rounded-full flex items-center justify-center font-bold transition-all duration-300",
        sizeClasses[size],
        isActive && "ring-2 ring-primary",
        isSpeaking && "speaking-pulse shadow-glow",
        `bg-${character.color}/20 text-${character.color}`
      )}
      animate={isSpeaking ? {
        scale: [1, 1.1, 1],
        boxShadow: [
          '0 0 20px hsl(var(--primary-glow) / 0.3)',
          '0 0 40px hsl(var(--primary-glow) / 0.6)',
          '0 0 20px hsl(var(--primary-glow) / 0.3)'
        ]
      } : {}}
      transition={{ 
        duration: 1.5, 
        repeat: isSpeaking ? Infinity : 0,
        ease: "easeInOut"
      }}
    >
      <span className="select-none">{character.avatar}</span>
    </motion.div>
  );
}